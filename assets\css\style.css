/* ===== CSS变量定义 ===== */
:root {
    /* 主色调 - 蓝色系专业感 */
    --primary-color: #007AFF;
    --primary-light: #5AC8FA;
    --primary-dark: #0051D5;
    --primary-gradient: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
    
    /* 辅助色 - 橙色系活力感 */
    --secondary-color: #FF9500;
    --secondary-light: #FFCC02;
    --secondary-dark: #FF6B00;
    --secondary-gradient: linear-gradient(135deg, #FF9500 0%, #FFCC02 100%);
    
    /* 中性色 */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    
    /* 状态色 */
    --success-color: #10B981;
    --warning-color: #F59E0B;
    --error-color: #EF4444;
    --info-color: #3B82F6;
    
    /* 字体 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* 间距 */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 过渡动画 */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* ===== 基础样式重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== 容器和布局 ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

.section {
    padding: var(--spacing-20) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-header h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== 加载动画 ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-normal);
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 导航栏 ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    text-decoration: none;
}

.nav-brand i {
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-8);
}

.nav-link {
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: var(--primary-color);
    border-radius: var(--radius-full);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 2px;
    background: var(--gray-700);
    transition: all var(--transition-fast);
}

/* ===== 按钮样式 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--secondary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-ghost {
    background: transparent;
    color: var(--gray-600);
    border: 1px solid var(--gray-300);
}

.btn-ghost:hover {
    background: var(--gray-100);
    border-color: var(--gray-400);
}

.btn-large {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-lg);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* ===== 表单元素 ===== */
.form-input {
    width: 100%;
    padding: var(--spacing-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    background: var(--white);
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.input-group {
    display: flex;
    gap: var(--spacing-3);
    align-items: center;
}

/* ===== 主要内容区域 ===== */
.main-content {
    margin-top: 70px;
}

/* ===== 首页英雄区域 ===== */
.hero-section {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    padding: var(--spacing-20) 0;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.highlight {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-12);
}

.hero-stats {
    display: flex;
    gap: var(--spacing-8);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

/* ===== 英雄区域卡片 ===== */
.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-8);
    width: 100%;
    max-width: 400px;
}

.card-header {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-6);
}

.card-dots {
    display: flex;
    gap: var(--spacing-2);
}

.card-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--gray-300);
}

.card-dots span:first-child {
    background: var(--error-color);
}

.card-dots span:nth-child(2) {
    background: var(--warning-color);
}

.card-dots span:last-child {
    background: var(--success-color);
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
    background: var(--gray-50);
    transition: all var(--transition-fast);
}

.feature-item:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateX(5px);
}

.feature-item i {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
    transition: color var(--transition-fast);
}

.feature-item:hover i {
    color: var(--white);
}

/* ===== 商户输入区域 ===== */
.merchant-input-section {
    margin-bottom: var(--spacing-12);
    display: flex;
    justify-content: center;
}

.merchant-input-section .input-group {
    max-width: 500px;
    width: 100%;
}

/* ===== 商户网格 ===== */
.merchants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-6);
}

.merchant-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    cursor: pointer;
    border: 1px solid var(--gray-200);
}

.merchant-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.merchant-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-4);
}

.merchant-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-xl);
}

.merchant-info h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.merchant-id {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.merchant-description {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--spacing-4);
}

.merchant-stats {
    display: flex;
    justify-content: space-between;
    padding-top: var(--spacing-4);
    border-top: 1px solid var(--gray-200);
}

.merchant-stat {
    text-align: center;
}

.merchant-stat-number {
    display: block;
    font-weight: 600;
    color: var(--primary-color);
}

.merchant-stat-label {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .merchants-grid {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 0 var(--spacing-3);
    }
    
    .section {
        padding: var(--spacing-12) 0;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .hero-description {
        font-size: var(--font-size-base);
    }

    .btn-large {
        padding: var(--spacing-3) var(--spacing-6);
        font-size: var(--font-size-base);
    }

    .input-group {
        flex-direction: column;
    }
}

/* ===== 店铺页面样式 ===== */
.shop-header {
    margin-bottom: var(--spacing-12);
}

.shop-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    margin-top: var(--spacing-6);
}

.shop-avatar {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-2xl);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-3xl);
}

.shop-details h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.shop-details p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-3);
    line-height: 1.6;
}

.shop-meta {
    display: flex;
    gap: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

/* ===== 商品网格 ===== */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-6);
}

.product-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    cursor: pointer;
    border: 1px solid var(--gray-200);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.product-image {
    height: 200px;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-4xl);
    color: var(--gray-400);
}

.product-content {
    padding: var(--spacing-6);
}

.product-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
    line-height: 1.4;
}

.product-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-3);
}

.product-price .price-symbol {
    font-size: var(--font-size-base);
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--spacing-4);
}

.product-stock {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.stock-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
}

.stock-indicator.low {
    background: var(--warning-color);
}

.stock-indicator.out {
    background: var(--error-color);
}

.product-description {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--spacing-4);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-actions {
    display: flex;
    gap: var(--spacing-2);
}

.product-actions .btn {
    flex: 1;
    font-size: var(--font-size-sm);
    padding: var(--spacing-2) var(--spacing-4);
}

/* ===== 商品详情页面 ===== */
.product-detail-content {
    max-width: 800px;
    margin: 0 auto;
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-10);
    box-shadow: var(--shadow-xl);
}

.product-detail-content h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.product-detail-content .product-meta {
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
}

.product-detail-content .product-price {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-8);
}

.product-description h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.product-description p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-8);
}

.product-actions {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.product-share {
    text-align: center;
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
}

/* ===== 订单确认页面 ===== */
.order-card {
    max-width: 600px;
    margin: 0 auto;
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-10);
    box-shadow: var(--shadow-xl);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-8);
    padding-bottom: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.order-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
}

.order-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--warning-color);
    font-weight: 500;
}

.order-info {
    margin-bottom: var(--spacing-8);
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4) 0;
    border-bottom: 1px solid var(--gray-100);
}

.order-item:last-child {
    border-bottom: none;
}

.order-item .label {
    color: var(--gray-600);
    font-weight: 500;
}

.order-item .value {
    color: var(--gray-900);
    font-weight: 600;
}

.order-item .value.price {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.order-actions {
    display: flex;
    gap: var(--spacing-4);
}

/* ===== 支付结果页面 ===== */
.result-card {
    max-width: 600px;
    margin: 0 auto;
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-10);
    box-shadow: var(--shadow-xl);
    text-align: center;
}

.result-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-6);
}

.result-icon.success {
    color: var(--success-color);
}

.result-icon.error {
    color: var(--error-color);
}

.result-card h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-8);
}

.result-card h2.success {
    color: var(--success-color);
}

.result-card h2.error {
    color: var(--error-color);
}

.result-info {
    text-align: left;
    margin-bottom: var(--spacing-8);
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3) 0;
    border-bottom: 1px solid var(--gray-100);
}

.result-item:last-child {
    border-bottom: none;
}

.delivery-section {
    margin-bottom: var(--spacing-8);
    text-align: left;
}

.delivery-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.delivery-content {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4);
    border: 1px solid var(--gray-200);
}

.delivery-content pre {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    white-space: pre-wrap;
    word-break: break-all;
}

.result-actions {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
}
