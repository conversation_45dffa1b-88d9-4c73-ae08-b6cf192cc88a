<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mika 寄售商城 - 专业安全的在线交易平台</title>
    <meta name="description" content="Mika寄售商城，专业安全的在线交易平台，7x24h自动发货，支持微信支付和支付宝，交易完全匿名">
    <meta name="keywords" content="寄售,商城,在线交易,自动发货,匿名交易">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- 加载动画 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>正在加载...</p>
        </div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-store"></i>
                <span>Mika 寄售商城</span>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link active">首页</a>
                <a href="#products" class="nav-link">商品</a>
                <a href="#about" class="nav-link">关于</a>
                <button id="loginBtn" class="btn btn-primary">商户登录</button>
            </div>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <!-- 首页区域 -->
        <section id="home" class="hero-section">
            <div class="container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title">
                            专业安全的
                            <span class="highlight">在线寄售平台</span>
                        </h1>
                        <p class="hero-description">
                            7x24h自动发货 • 售后稳定 • 交易完全匿名<br>
                            一站式托管发卡平台，零门槛入驻
                        </p>
                        <div class="hero-buttons">
                            <button id="startShoppingBtn" class="btn btn-primary btn-large">
                                <i class="fas fa-shopping-cart"></i>
                                开始购物
                            </button>
                            <button id="becomeSellerBtn" class="btn btn-secondary btn-large">
                                <i class="fas fa-store"></i>
                                成为商户
                            </button>
                        </div>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <span class="stat-number" id="merchantCount">1000+</span>
                                <span class="stat-label">活跃商户</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="productCount">5000+</span>
                                <span class="stat-label">在售商品</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="orderCount">50000+</span>
                                <span class="stat-label">成功订单</span>
                            </div>
                        </div>
                    </div>
                    <div class="hero-image">
                        <div class="hero-card">
                            <div class="card-header">
                                <div class="card-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="feature-list">
                                    <div class="feature-item">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>安全保障</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-clock"></i>
                                        <span>24小时服务</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-user-secret"></i>
                                        <span>匿名交易</span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-rocket"></i>
                                        <span>自动发货</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 商户选择区域 -->
        <section id="merchantSelect" class="section" style="display: none;">
            <div class="container">
                <div class="section-header">
                    <h2>选择商户</h2>
                    <p>请输入商户ID或从推荐商户中选择</p>
                </div>
                <div class="merchant-input-section">
                    <div class="input-group">
                        <input type="text" id="merchantIdInput" placeholder="请输入商户ID" class="form-input">
                        <button id="searchMerchantBtn" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                    </div>
                </div>
                <div id="recommendedMerchants" class="merchants-grid">
                    <!-- 推荐商户将通过JavaScript动态加载 -->
                </div>
            </div>
        </section>

        <!-- 商户店铺区域 -->
        <section id="merchantShop" class="section" style="display: none;">
            <div class="container">
                <div class="shop-header">
                    <button id="backToMerchants" class="btn btn-ghost">
                        <i class="fas fa-arrow-left"></i>
                        返回
                    </button>
                    <div class="shop-info">
                        <div class="shop-avatar">
                            <i class="fas fa-store"></i>
                        </div>
                        <div class="shop-details">
                            <h2 id="shopName">商店名称</h2>
                            <p id="shopDescription">商店介绍</p>
                            <div class="shop-meta">
                                <span class="shop-id">ID: <span id="currentMerchantId"></span></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="productsGrid" class="products-grid">
                    <!-- 商品列表将通过JavaScript动态加载 -->
                </div>
            </div>
        </section>

        <!-- 商品详情区域 -->
        <section id="productDetail" class="section" style="display: none;">
            <div class="container">
                <button id="backToProducts" class="btn btn-ghost">
                    <i class="fas fa-arrow-left"></i>
                    返回商品列表
                </button>
                <div class="product-detail-content">
                    <div class="product-info">
                        <h2 id="productName">商品名称</h2>
                        <div class="product-meta">
                            <span class="product-id">商品编号: <span id="productId"></span></span>
                            <span class="product-stock">库存: <span id="productStock"></span> 件</span>
                        </div>
                        <div class="product-price">
                            <span class="price-symbol">¥</span>
                            <span id="productPrice">0.00</span>
                        </div>
                        <div class="product-description">
                            <h3>商品介绍</h3>
                            <p id="productDesc">商品描述</p>
                        </div>
                        <div class="product-actions">
                            <button id="buyWithWechat" class="btn btn-primary btn-large">
                                <i class="fab fa-weixin"></i>
                                微信支付
                            </button>
                            <button id="buyWithAlipay" class="btn btn-secondary btn-large">
                                <i class="fab fa-alipay"></i>
                                支付宝
                            </button>
                        </div>
                        <div class="product-share">
                            <button id="shareProduct" class="btn btn-ghost">
                                <i class="fas fa-share-alt"></i>
                                分享商品
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 订单确认区域 -->
        <section id="orderConfirm" class="section" style="display: none;">
            <div class="container">
                <div class="order-card">
                    <div class="order-header">
                        <h2>订单确认</h2>
                        <div class="order-status">
                            <i class="fas fa-clock"></i>
                            <span>等待支付</span>
                        </div>
                    </div>
                    <div class="order-info">
                        <div class="order-item">
                            <span class="label">订单号:</span>
                            <span id="orderId" class="value">-</span>
                        </div>
                        <div class="order-item">
                            <span class="label">商品名称:</span>
                            <span id="orderProductName" class="value">-</span>
                        </div>
                        <div class="order-item">
                            <span class="label">支付金额:</span>
                            <span id="orderAmount" class="value price">¥0.00</span>
                        </div>
                        <div class="order-item">
                            <span class="label">创建时间:</span>
                            <span id="orderTime" class="value">-</span>
                        </div>
                    </div>
                    <div class="order-actions">
                        <button id="payNowBtn" class="btn btn-primary btn-large">
                            <i class="fas fa-credit-card"></i>
                            立即支付
                        </button>
                        <button id="checkPaymentBtn" class="btn btn-secondary">
                            <i class="fas fa-check"></i>
                            我已支付
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 支付结果区域 -->
        <section id="paymentResult" class="section" style="display: none;">
            <div class="container">
                <div class="result-card">
                    <div class="result-icon">
                        <i id="resultIcon" class="fas fa-check-circle"></i>
                    </div>
                    <h2 id="resultTitle">支付成功！</h2>
                    <div class="result-info">
                        <div class="result-item">
                            <span class="label">订单号:</span>
                            <span id="resultOrderId" class="value">-</span>
                        </div>
                        <div class="result-item">
                            <span class="label">支付时间:</span>
                            <span id="resultPayTime" class="value">-</span>
                        </div>
                    </div>
                    <div id="deliveryContent" class="delivery-section" style="display: none;">
                        <h3>发货内容</h3>
                        <div class="delivery-content">
                            <pre id="deliveryText"></pre>
                        </div>
                    </div>
                    <div class="result-actions">
                        <button id="backToHomeBtn" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            返回首页
                        </button>
                        <button id="complaintBtn" class="btn btn-ghost">
                            <i class="fas fa-exclamation-triangle"></i>
                            投诉订单
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 关于我们区域 -->
        <section id="about" class="section">
            <div class="container">
                <div class="section-header">
                    <h2>关于我们</h2>
                    <p>专业、安全、可靠的在线寄售平台</p>
                </div>
                <div class="about-content">
                    <div class="about-features">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3>安全保障</h3>
                            <p>采用先进的加密技术，确保交易安全，保护用户隐私</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h3>24小时服务</h3>
                            <p>7x24小时自动发货系统，随时随地完成交易</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-user-secret"></i>
                            </div>
                            <h3>匿名交易</h3>
                            <p>完全匿名的交易环境，保护买卖双方隐私</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <h3>专业客服</h3>
                            <p>专业的客服团队，及时解决您的问题</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <i class="fas fa-store"></i>
                    <span>Mika 寄售商城</span>
                </div>
                <div class="footer-links">
                    <a href="#" class="footer-link">服务条款</a>
                    <a href="#" class="footer-link">隐私政策</a>
                    <a href="#" class="footer-link">联系我们</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Mika 寄售商城. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p id="modalMessage">内容</p>
            </div>
            <div class="modal-footer">
                <button id="modalConfirm" class="btn btn-primary">确定</button>
                <button id="modalCancel" class="btn btn-ghost">取消</button>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div id="toast" class="toast">
        <div class="toast-content">
            <i id="toastIcon" class="fas fa-info-circle"></i>
            <span id="toastMessage">消息</span>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
</body>
</html>
